import React, { useState } from 'react'
import {
  Box,
  VStack,
  HStack,
  Heading,
  Checkbox,
  Textarea,
  Button,
  Select,
  Text,
  Badge,
  Divider,
  useToast,
  FormControl,
  FormLabel,
  Alert,
  AlertIcon,
  Spinner
} from '@chakra-ui/react'
import { useTracker } from '../context/TrackerContext'

const InteractiveTracker: React.FC = () => {
  const { state, toggleTodo, addSymptom, updateNotes, syncWithBackend } = useTracker()
  const [newSymptom, setNewSymptom] = useState('')
  const [symptomSeverity, setSymptomSeverity] = useState<'mild' | 'moderate' | 'severe'>('mild')
  const [patientId] = useState('demo-patient-001') // In a real app, this would come from authentication

  // Simple toast-like notification function for testing
  const showToast = (title: string, description: string, status: string) => {
    console.log(`Toast: ${title} - ${description} (${status})`)
  }

  const handleTodoToggle = (todoId: string) => {
    const todo = state.data.todos.find(t => t.id === todoId)
    toggleTodo(todoId)

    if (todo) {
      showToast(
        todo.completed ? 'Task unmarked' : 'Task completed!',
        todo.text,
        todo.completed ? 'info' : 'success'
      )
    }
  }

  const handleAddSymptom = () => {
    if (!newSymptom.trim()) return

    addSymptom(newSymptom.trim(), symptomSeverity)
    setNewSymptom('')
    setSymptomSeverity('mild')

    showToast(
      'Symptom logged',
      `${newSymptom} (${symptomSeverity})`,
      'info'
    )
  }

  const handleNotesChange = (value: string) => {
    updateNotes(value)
  }

  const handleSyncWithBackend = async () => {
    try {
      await syncWithBackend(patientId)
      showToast(
        'Sync successful',
        'Your data has been saved to the server',
        'success'
      )
    } catch (error) {
      showToast(
        'Sync failed',
        'Could not save to server. Data is saved locally.',
        'warning'
      )
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'mild': return 'green'
      case 'moderate': return 'yellow'
      case 'severe': return 'red'
      default: return 'gray'
    }
  }

  return (
    <Box
      p={6}
      bg="white"
      borderWidth={1}
      borderColor="gray.200"
      borderRadius="lg"
      shadow="sm"
    >
      <VStack align="stretch" spacing={6}>
        <HStack justify="space-between" align="center">
          <Heading as="h3" size="md" color="blue.600">
            Recovery Tracker
          </Heading>
          {state.loading && <Spinner size="sm" />}
        </HStack>

        {/* Error Alert */}
        {state.error && (
          <Alert status="warning" size="sm" borderRadius="md">
            <AlertIcon />
            <Text fontSize="sm">{state.error}</Text>
          </Alert>
        )}

        {/* Sync Button */}
        <Button
          onClick={handleSyncWithBackend}
          size="sm"
          variant="outline"
          colorScheme="blue"
          isLoading={state.loading}
          loadingText="Syncing..."
        >
          Sync with Server
        </Button>

        {/* To-Do Checklist */}
        <Box>
          <Heading as="h4" size="sm" mb={3} color="gray.600">
            Daily Care Tasks
          </Heading>
          <VStack align="stretch" spacing={2}>
            {state.data.todos.map(todo => (
              <Checkbox
                key={todo.id}
                isChecked={todo.completed}
                onChange={() => handleTodoToggle(todo.id)}
                colorScheme="blue"
                size="md"
              >
                <Text
                  textDecoration={todo.completed ? 'line-through' : 'none'}
                  color={todo.completed ? 'gray.500' : 'inherit'}
                >
                  {todo.text}
                </Text>
              </Checkbox>
            ))}
          </VStack>
        </Box>

        <Divider />

        {/* Symptom Tracker */}
        <Box>
          <Heading as="h4" size="sm" mb={3} color="gray.600">
            Symptom Tracker
          </Heading>
          <VStack align="stretch" spacing={3}>
            <HStack>
              <FormControl flex={2}>
                <FormLabel fontSize="sm">Symptom</FormLabel>
                <Textarea
                  value={newSymptom}
                  onChange={(e) => setNewSymptom(e.target.value)}
                  placeholder="Describe any symptoms you're experiencing..."
                  size="sm"
                  rows={2}
                />
              </FormControl>
              <FormControl flex={1}>
                <FormLabel fontSize="sm">Severity</FormLabel>
                <Select
                  value={symptomSeverity}
                  onChange={(e) => setSymptomSeverity(e.target.value as 'mild' | 'moderate' | 'severe')}
                  size="sm"
                >
                  <option value="mild">Mild</option>
                  <option value="moderate">Moderate</option>
                  <option value="severe">Severe</option>
                </Select>
              </FormControl>
            </HStack>
            <Button
              onClick={handleAddSymptom}
              colorScheme="blue"
              size="sm"
              isDisabled={!newSymptom.trim()}
            >
              Log Symptom
            </Button>

            {/* Recent Symptoms */}
            {state.data.symptoms.length > 0 && (
              <Box mt={4}>
                <Text fontSize="sm" fontWeight="semibold" mb={2}>Recent Symptoms:</Text>
                <VStack align="stretch" spacing={2} maxH="200px" overflowY="auto">
                  {state.data.symptoms.slice(0, 5).map(symptom => (
                    <Box key={symptom.id} p={2} bg="gray.50" borderRadius="md">
                      <HStack justify="space-between">
                        <Text fontSize="sm">{symptom.symptom}</Text>
                        <Badge colorScheme={getSeverityColor(symptom.severity)} size="sm">
                          {symptom.severity}
                        </Badge>
                      </HStack>
                      <Text fontSize="xs" color="gray.500">
                        {symptom.timestamp.toLocaleString()}
                      </Text>
                    </Box>
                  ))}
                </VStack>
              </Box>
            )}
          </VStack>
        </Box>

        <Divider />

        {/* Notes Section */}
        <Box>
          <FormControl>
            <FormLabel fontSize="sm" fontWeight="semibold">Personal Notes</FormLabel>
            <Textarea
              value={state.data.notes}
              onChange={(e) => handleNotesChange(e.target.value)}
              placeholder="Add any personal notes about your recovery..."
              rows={4}
              resize="vertical"
            />
          </FormControl>
        </Box>

        {/* Status Information */}
        <VStack spacing={1} align="center">
          <Text fontSize="xs" color="gray.500">
            Last updated: {state.data.lastUpdated.toLocaleString()}
          </Text>
          {state.lastSyncedWithBackend && (
            <Text fontSize="xs" color="green.500">
              Last synced: {state.lastSyncedWithBackend.toLocaleString()}
            </Text>
          )}
        </VStack>
      </VStack>
    </Box>
  )
}

export default InteractiveTracker
