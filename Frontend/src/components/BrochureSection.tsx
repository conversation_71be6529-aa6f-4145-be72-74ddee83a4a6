import React from 'react'
import {
  Box,
  Heading,
  VStack,
  UnorderedList,
  ListItem,
  Text,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Badge
} from '@chakra-ui/react'

interface BrochureSectionProps {
  title: string
  items: string[] | Record<string, string>
  type?: 'list' | 'timeline' | 'warning'
  isCollapsible?: boolean
  defaultExpanded?: boolean
}

const BrochureSection: React.FC<BrochureSectionProps> = ({
  title,
  items,
  type = 'list',
  isCollapsible = false,
  defaultExpanded = true
}) => {
  const getBackgroundColor = () => {
    if (type === 'warning') return 'red.50'
    return 'white'
  }

  const getBorderColor = () => {
    if (type === 'warning') return 'red.200'
    return 'gray.200'
  }

  const renderContent = () => {
    if (Array.isArray(items)) {
      return (
        <UnorderedList spacing={3} pl={4}>
          {items.map((item, index) => (
            <ListItem key={index} fontSize="md" lineHeight="1.6">
              <Text>{item}</Text>
            </ListItem>
          ))}
        </UnorderedList>
      )
    } else {
      // Handle timeline/object format
      return (
        <VStack align="stretch" spacing={3}>
          {Object.entries(items).map(([key, value]) => (
            <Box key={key} p={3} bg="gray.50" borderRadius="md">
              <Text fontWeight="semibold" mb={1} textTransform="capitalize">
                {key.replace(/([A-Z])/g, ' $1').trim()}:
              </Text>
              <Text fontSize="sm" color="gray.600">
                {value}
              </Text>
            </Box>
          ))}
        </VStack>
      )
    }
  }

  const content = (
    <Box
      p={6}
      bg={getBackgroundColor()}
      borderWidth={2}
      borderColor={getBorderColor()}
      borderRadius="lg"
      shadow="sm"
      role="region"
      aria-labelledby={`section-${title.replace(/\s+/g, '-').toLowerCase()}`}
    >
      <VStack align="stretch" spacing={4}>
        <Box display="flex" alignItems="center" gap={3}>
          <Heading
            as="h3"
            size="md"
            id={`section-${title.replace(/\s+/g, '-').toLowerCase()}`}
            color={type === 'warning' ? 'red.600' : 'blue.600'}
          >
            {title}
          </Heading>
          {type === 'warning' && (
            <Badge colorScheme="red" variant="solid" fontSize="xs">
              Important
            </Badge>
          )}
        </Box>
        {renderContent()}
      </VStack>
    </Box>
  )

  if (isCollapsible) {
    return (
      <Accordion allowToggle defaultIndex={defaultExpanded ? [0] : []}>
        <AccordionItem border="none">
          <AccordionButton
            p={0}
            _hover={{ bg: 'transparent' }}
            _focus={{ boxShadow: 'outline' }}
          >
            <Box flex="1">
              {content}
            </Box>
            <AccordionIcon ml={2} />
          </AccordionButton>
          <AccordionPanel p={0} pt={4}>
            {/* Additional content can go here if needed */}
          </AccordionPanel>
        </AccordionItem>
      </Accordion>
    )
  }

  return content
}

export default BrochureSection
