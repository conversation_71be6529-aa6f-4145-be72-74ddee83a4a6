import React, { useState, useEffect } from 'react'
import {
  Box,
  Container,
  Heading,
  VStack,
  Grid,
  GridItem,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
  Text,
  useToast,
  useBreakpointValue
} from '@chakra-ui/react'
import BrochureSection from './components/BrochureSection'
import InteractiveTracker from './components/InteractiveTracker'
import { TrackerProvider } from './context/TrackerContext'
import { brochureApi, checkApiConnectivity, handleApiError } from './services/api'
import type { BrochureData } from './types'

function App() {
  const [brochureData, setBrochureData] = useState<BrochureData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [apiConnected, setApiConnected] = useState(false)
  const toast = useToast()

  // Responsive grid layout
  const gridTemplateColumns = useBreakpointValue({
    base: '1fr',
    lg: '2fr 1fr'
  })

  const gridGap = useBreakpointValue({
    base: 6,
    lg: 8
  })

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Check API connectivity
        const connected = await checkApiConnectivity()
        setApiConnected(connected)

        if (connected) {
          // Fetch brochure data
          const data = await brochureApi.getMyomectomyBrochure()
          setBrochureData(data)
        } else {
          // Use fallback data if API is not available
          setBrochureData(getFallbackBrochureData())
          toast({
            title: 'Offline Mode',
            description: 'Using cached content. Some features may be limited.',
            status: 'warning',
            duration: 5000,
            isClosable: true,
          })
        }
      } catch (err) {
        const errorMessage = handleApiError(err)
        setError(errorMessage)
        setBrochureData(getFallbackBrochureData())
        toast({
          title: 'Error loading content',
          description: errorMessage,
          status: 'error',
          duration: 5000,
          isClosable: true,
        })
      } finally {
        setLoading(false)
      }
    }

    initializeApp()
  }, [toast])

  // Fallback data for offline mode
  const getFallbackBrochureData = (): BrochureData => ({
    title: "Myomectomy Post-Operative Care Instructions",
    lastUpdated: "2025-01-25",
    activityRestrictions: [
      "Lifting: Avoid lifting more than 1 gallon of milk (approximately 8 lbs) for 4-6 weeks",
      "Driving: Restricted until pain medications are discontinued and you can safely operate a vehicle",
      "Intercourse: No sexual activity for 6 weeks to allow proper healing",
      "Exercise: No strenuous exercise or heavy lifting for 4-6 weeks",
      "Work: Return to desk work when comfortable, typically 1-2 weeks; physical work may require 4-6 weeks"
    ],
    painManagement: [
      "Take prescribed pain medications as directed by your physician",
      "Use stool softeners if experiencing constipation from pain medications",
      "Apply ice packs to incision area for 15-20 minutes every 2-3 hours for first 48 hours",
      "Take medications with food to prevent stomach upset"
    ],
    warningSigns: [
      "Fever greater than 100.4°F (38°C)",
      "Heavy bleeding requiring more than one pad per hour",
      "Severe unrelieved abdominal pain",
      "Signs of infection: increased redness, warmth, swelling, or drainage from incision"
    ],
    followUpSchedule: {
      "postOpAppointment": "Post-operative appointment scheduled at 4-6 weeks",
      "urgentCare": "Contact office immediately if warning signs occur",
      "routineQuestions": "Call office during business hours for routine questions"
    },
    healingTimeline: {
      "fullRecovery": "Full recovery typically takes 4-6 weeks",
      "dischargeTime": "Hospital discharge usually occurs 4-8 hours after procedure for outpatient cases",
      "returnToNormalActivity": "Gradual return to normal activities over 4-6 weeks"
    },
    dietaryGuidelines: [
      "Start with clear liquids and advance to regular diet as tolerated",
      "Increase fiber intake to prevent constipation",
      "Stay well hydrated - drink 8-10 glasses of water daily",
      "Avoid alcohol while taking pain medications"
    ],
    incisionCare: [
      "Keep incision clean and dry",
      "Gently wash with soap and water during shower",
      "Pat dry - do not rub the incision area",
      "Do not apply lotions, creams, or ointments unless prescribed"
    ]
  })

  if (loading) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack spacing={4} align="center" minH="50vh" justify="center">
          <Spinner size="xl" color="brand.500" />
          <Text>Loading your aftercare information...</Text>
        </VStack>
      </Container>
    )
  }

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box textAlign="center">
          <Heading as="h1" size="xl" color="brand.600" mb={2}>
            Aftercare Patient Management
          </Heading>
          <Text color="gray.600" fontSize="lg">
            Your personalized post-operative care guide and recovery tracker
          </Text>
          {!apiConnected && (
            <Alert status="warning" mt={4} borderRadius="md">
              <AlertIcon />
              <AlertTitle>Offline Mode</AlertTitle>
              <AlertDescription>
                Some features may be limited. Check your internet connection.
              </AlertDescription>
            </Alert>
          )}
        </Box>

        {error && (
          <Alert status="error" borderRadius="md">
            <AlertIcon />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Main Content Grid */}
        <Grid
          templateColumns={gridTemplateColumns}
          gap={gridGap}
          alignItems="start"
        >
          {/* Left Column - Brochure Content */}
          <GridItem>
            <VStack spacing={6} align="stretch">
              {brochureData && (
                <>
                  <BrochureSection
                    title="Activity Restrictions"
                    items={brochureData.activityRestrictions}
                    type="list"
                  />

                  <BrochureSection
                    title="Warning Signs - Contact Your Doctor Immediately"
                    items={brochureData.warningSigns}
                    type="warning"
                  />

                  <BrochureSection
                    title="Pain Management"
                    items={brochureData.painManagement}
                    type="list"
                  />

                  <BrochureSection
                    title="Follow-up Schedule"
                    items={brochureData.followUpSchedule}
                    type="timeline"
                  />

                  <BrochureSection
                    title="Healing Timeline"
                    items={brochureData.healingTimeline}
                    type="timeline"
                  />

                  <BrochureSection
                    title="Dietary Guidelines"
                    items={brochureData.dietaryGuidelines}
                    type="list"
                  />

                  <BrochureSection
                    title="Incision Care"
                    items={brochureData.incisionCare}
                    type="list"
                  />
                </>
              )}
            </VStack>
          </GridItem>

          {/* Right Column - Interactive Tracker */}
          <GridItem>
            <Box position="sticky" top={4}>
              <TrackerProvider>
                <InteractiveTracker />
              </TrackerProvider>
            </Box>
          </GridItem>
        </Grid>
      </VStack>
    </Container>
  )
}

export default App
