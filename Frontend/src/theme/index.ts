import { extendTheme } from '@chakra-ui/react'

// Custom theme for the aftercare application
const theme = extendTheme({
  colors: {
    brand: {
      50: '#e6f3ff',
      100: '#b3d9ff',
      200: '#80bfff',
      300: '#4da6ff',
      400: '#1a8cff',
      500: '#0073e6',
      600: '#005bb3',
      700: '#004280',
      800: '#002a4d',
      900: '#00111a',
    },
    medical: {
      emergency: '#e53e3e',
      warning: '#dd6b20',
      info: '#3182ce',
      success: '#38a169',
    }
  },
  fonts: {
    heading: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif',
    body: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif',
  },
  fontSizes: {
    xs: '0.75rem',
    sm: '0.875rem',
    md: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem',
  },
  space: {
    px: '1px',
    0.5: '0.125rem',
    1: '0.25rem',
    1.5: '0.375rem',
    2: '0.5rem',
    2.5: '0.625rem',
    3: '0.75rem',
    3.5: '0.875rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    7: '1.75rem',
    8: '2rem',
    9: '2.25rem',
    10: '2.5rem',
    12: '3rem',
    14: '3.5rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
    36: '9rem',
    40: '10rem',
    44: '11rem',
    48: '12rem',
    52: '13rem',
    56: '14rem',
    60: '15rem',
    64: '16rem',
    72: '18rem',
    80: '20rem',
    96: '24rem',
  },
  components: {
    Button: {
      defaultProps: {
        colorScheme: 'brand',
      },
      variants: {
        solid: {
          _focus: {
            boxShadow: '0 0 0 3px rgba(0, 115, 230, 0.6)',
          },
        },
        outline: {
          _focus: {
            boxShadow: '0 0 0 3px rgba(0, 115, 230, 0.6)',
          },
        },
      },
    },
    Checkbox: {
      defaultProps: {
        colorScheme: 'brand',
      },
      variants: {
        solid: {
          control: {
            _focus: {
              boxShadow: '0 0 0 3px rgba(0, 115, 230, 0.6)',
            },
          },
        },
      },
    },
    Input: {
      variants: {
        outline: {
          field: {
            _focus: {
              borderColor: 'brand.500',
              boxShadow: '0 0 0 1px #0073e6',
            },
          },
        },
      },
    },
    Textarea: {
      variants: {
        outline: {
          _focus: {
            borderColor: 'brand.500',
            boxShadow: '0 0 0 1px #0073e6',
          },
        },
      },
    },
    Select: {
      variants: {
        outline: {
          field: {
            _focus: {
              borderColor: 'brand.500',
              boxShadow: '0 0 0 1px #0073e6',
            },
          },
        },
      },
    },
    Heading: {
      baseStyle: {
        fontWeight: '600',
        lineHeight: '1.2',
      },
    },
    Text: {
      baseStyle: {
        lineHeight: '1.6',
      },
    },
  },
  styles: {
    global: {
      body: {
        bg: 'gray.50',
        color: 'gray.800',
      },
      '*': {
        boxSizing: 'border-box',
      },
      '*:focus': {
        outline: 'none',
      },
    },
  },
  config: {
    initialColorMode: 'light',
    useSystemColorMode: false,
  },
})

export default theme
