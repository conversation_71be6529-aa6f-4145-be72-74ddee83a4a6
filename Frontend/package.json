{"name": "aftercare", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@vitejs/plugin-react": "^4.7.0", "@vitest/ui": "^3.2.4", "jsdom": "^26.1.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vitest": "^3.2.4"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.10.9", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "framer-motion": "^12.23.9", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0"}}