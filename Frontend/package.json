{"name": "aftercare", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"typescript": "~5.8.3", "vite": "^7.0.4"}, "dependencies": {"@chakra-ui/react": "^3.22.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "framer-motion": "^12.23.9", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0"}}