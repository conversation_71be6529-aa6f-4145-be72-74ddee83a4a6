{"name": "aftercare-backend", "version": "1.0.0", "description": "Backend API for Aftercare Patient Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test/api.test.js"}, "keywords": ["aftercare", "patient", "medical", "api"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "helmet": "^8.1.0", "mongodb": "^6.18.0", "morgan": "^1.10.1"}, "devDependencies": {"nodemon": "^3.1.10"}}